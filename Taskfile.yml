version: "3"

vars:
  APP_NAME: "ns-drive"
  BIN_DIR: "bin"
  VITE_PORT: "{{.WAILS_VITE_PORT | default 9245}}"

tasks:
  build:
    summary: Builds the application for production
    dir: desktop
    cmds:
      - echo "Installing frontend dependencies..."
      - cd frontend && yarn install
      - echo "Building frontend..."
      - cd frontend && yarn build
      - echo "Generating Wails bindings..."
      - wails3 generate bindings
      - echo "Building application..."
      - go build -ldflags="-s -w" -o {{.BIN_DIR}}/{{.APP_NAME}}
    env:
      CGO_CFLAGS: "-mmacosx-version-min=16.0"
      CGO_LDFLAGS: "-mmacosx-version-min=16.0"
      MACOSX_DEPLOYMENT_TARGET: "16.0"

  dev:fe:
    summary: Starts the frontend development server
    dir: desktop/frontend
    cmds:
      - echo "Starting frontend development server on port {{.VITE_PORT}}..."
      - yarn start --port {{.VITE_PORT}}

  dev:be:
    summary: Runs the application in development mode (run 'task dev:frontend' in another terminal first)
    dir: desktop
    cmds:
      - echo "Starting Wails development mode..."
      - echo "Make sure frontend dev server is running on port {{.VITE_PORT}}"
      - '{{env "GOPATH"}}/bin/wails3 dev -config ./build/config.yml -port {{.VITE_PORT}}'

  lint:fe:
    summary: Runs ESLint on the frontend code
    dir: desktop/frontend
    cmds:
      - echo "Running frontend linting..."
      - yarn lint

  lint:be:
    summary: Runs golangci-lint on the backend code
    dir: desktop
    cmds:
      - echo "Running backend linting..."
      - golangci-lint run

  lint:
    summary: Runs linting on both frontend and backend
    cmds:
      - task: lint:fe
      - task: lint:be
